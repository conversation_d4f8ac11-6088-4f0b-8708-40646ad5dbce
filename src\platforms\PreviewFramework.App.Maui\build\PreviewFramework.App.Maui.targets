<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target Name="GeneratePreviewAppSettings"
          BeforeTargets="CoreCompile"
          Inputs="$(MSBuildProjectFullPath)"
          Outputs="$(IntermediateOutputPath)PreviewAppInitializer.g.cs">
    <PropertyGroup>
      <DevToolsConnectionSettingsPath>$(UserProfile)\.previewframework\devToolsConnectionSettings.json</DevToolsConnectionSettingsPath>
    </PropertyGroup>
    
    <!-- Force execution if the connection settings file doesn't exist -->
    <CallTarget Targets="_ForceGeneratePreviewAppSettings" 
                Condition="!Exists('$(DevToolsConnectionSettingsPath)')" />
                
    <GeneratePreviewAppSettingsTask
      ProjectPath="$(MSBuildProjectFullPath)"
      OutputPath="$(IntermediateOutputPath)PreviewAppInitializer.g.cs"
      PlatformPreviewApplication="PreviewFramework.App.Maui.MauiPreviewApplication.Instance" />
    <ItemGroup>
      <Compile Include="$(IntermediateOutputPath)PreviewAppInitializer.g.cs" />
    </ItemGroup>
  </Target>

  <!-- Empty target to force execution of the main target -->
  <Target Name="_ForceGeneratePreviewAppSettings" />
</Project>
